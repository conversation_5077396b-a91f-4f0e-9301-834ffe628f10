export interface Schedule {
  id: number;
  client_name: string;
  client_id: number;
  client_email?: string;
  client_phone?: string;
  service_name?: string;
  caregiver_id: number;
  start_time: string;
  end_time: string;
  location: {
    id: number;
    address: string;
    city: string;
    state: string;
    zip_code: string;
    latitude: number;
    longitude: number;
    created_at: string;
    updated_at: string;
  };
  location_id: number;
  status: 'scheduled' | 'in_progress' | 'completed' | 'missed' | 'cancelled';
  notes: string;
  created_at: string;
  updated_at: string;
  visit?: Visit;
  tasks?: Task[];
}

export interface Visit {
  id: number;
  schedule_id: number;
  start_time?: string;
  end_time?: string;
  start_latitude?: number;
  start_longitude?: number;
  end_latitude?: number;
  end_longitude?: number;
  status: 'not_started' | 'in_progress' | 'completed';
  notes?: string;
}

export interface Task {
  id: number;
  schedule_id: number;
  name: string;
  description: string;
  status: 'pending' | 'completed' | 'not_completed';
  reason?: string;
}

export interface ScheduleStats {
  total: number;
  missed: number;
  upcoming: number;
  completed: number;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface LocationData {
  latitude: number;
  longitude: number;
}

export interface StartVisitRequest {
  start_latitude: number;
  start_longitude: number;
}

export interface EndVisitRequest {
  end_latitude: number;
  end_longitude: number;
  notes?: string;
}

export interface CancelVisitRequest {
  reason?: string;
}

export interface UpdateTaskRequest {
  status: 'completed' | 'not_completed' | 'pending';
  reason?: string;
}
