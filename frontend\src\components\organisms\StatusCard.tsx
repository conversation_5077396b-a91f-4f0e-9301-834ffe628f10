import React from 'react';
import { View, StyleSheet } from 'react-native';
import { colors, spacing, borderRadius, shadows } from '../../constants';
import { Text, Button, Icon } from '../atoms';
import { UserInfo } from '../molecules';

interface StatusCardProps {
  user: {
    name: string;
    avatarUrl?: string;
  };
  location: string;
  timeRange: string;
  timer: string; // Format: "HH:MM:SS"
  onClockOut: () => void;
  isInProgress?: boolean;
}

const StatusCard: React.FC<StatusCardProps> = ({
  user,
  location,
  timeRange,
  timer,
  onClockOut,
  isInProgress = true
}) => {
  return (
    <View style={styles.container}>
      {/* Timer Display */}
      <View style={styles.timerSection}>
        <Text variant="h1" color="textOnPrimary" style={styles.timerText}>
          {timer}
        </Text>
      </View>

      {/* User Info */}
      <View style={styles.userSection}>
        <UserInfo
          name={user.name}
          avatarSource={user.avatarUrl ? { uri: user.avatarUrl } : undefined}
          size="medium"
          textColor="textOnPrimary"
          secondaryTextColor="textOnPrimary"
        />
      </View>

      {/* Location */}
      <View style={styles.locationSection}>
        <View style={styles.row}>
          <Icon name="location-outline" size={16} color="textOnPrimary" />
          <Text variant="bodySmall" color="textOnPrimary" style={styles.locationText}>
            {location}
          </Text>
        </View>
      </View>

      {/* Time Range */}
      <View style={styles.timeSection}>
        <View style={styles.row}>
          <Icon name="time-outline" size={16} color="textOnPrimary" />
          <Text variant="bodySmall" color="textOnPrimary" style={styles.timeText}>
            {timeRange}
          </Text>
        </View>
      </View>

      {/* Clock Out Button */}
      <Button
        variant="secondary"
        onPress={onClockOut}
        fullWidth
        style={styles.button}
      >
        <View style={styles.buttonContent}>
          <Icon name="time-outline" size={20} color="primary" />
          <Text variant="button" color="primary" style={styles.buttonText}>
            {isInProgress ? 'Clock-Out' : 'Clock-In'}
          </Text>
        </View>
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.xl,
    padding: spacing.xl,
    ...shadows.card,
  },
  timerSection: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  timerText: {
    fontSize: 24,
    padding: 10,
    fontWeight: '400',
    letterSpacing: 2,
  },
  userSection: {
    marginBottom: spacing.lg,
  },
  locationSection: {
    marginBottom: spacing.md,
  },
  timeSection: {
    marginBottom: spacing.xl,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    marginLeft: spacing.sm,
    flex: 1,
  },
  timeText: {
    marginLeft: spacing.sm,
    flex: 1,
  },
  button: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    paddingVertical: spacing.lg,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    marginLeft: spacing.sm,
  },
});

export default StatusCard;
