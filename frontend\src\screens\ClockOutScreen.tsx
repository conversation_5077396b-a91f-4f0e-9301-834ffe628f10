import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackScreenProps } from '@react-navigation/stack';
import { colors, spacing, borderRadius } from '../constants';
import { Text, Button, Icon } from '../components/atoms';
import { UserInfo, TaskItem } from '../components/molecules';
import { HomeStackParamList } from '../navigation/HomeStackNavigator';
import { useScheduleById, useEndVisit, useUpdateTaskStatus } from '../hooks';

type Props = StackScreenProps<HomeStackParamList, 'ClockOut'>;

const ClockOutScreen: React.FC<Props> = ({ route, navigation }) => {
  const { scheduleId } = route.params;
  const { data: schedule, isLoading, error } = useScheduleById(scheduleId);
  const endVisitMutation = useEndVisit();
  const updateTaskMutation = useUpdateTaskStatus();

  const [elapsedTime, setElapsedTime] = useState(0);
  const [pendingTaskUpdates, setPendingTaskUpdates] = useState<{ [key: number]: { status: 'completed' | 'not_completed', reason?: string } }>({});

  // Calculate elapsed time
  useEffect(() => {
    if (schedule?.visit?.start_time) {
      const startTime = new Date(schedule.visit.start_time).getTime();
      const updateTimer = () => {
        const now = Date.now();
        const elapsed = Math.floor((now - startTime) / 1000);
        setElapsedTime(elapsed);
      };

      updateTimer();
      const interval = setInterval(updateTimer, 1000);
      return () => clearInterval(interval);
    }
  }, [schedule?.visit?.start_time]);

  const formatElapsedTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')} : ${minutes.toString().padStart(2, '0')} : ${secs.toString().padStart(2, '0')}`;
  };

  const handleTaskStatusChange = (taskId: number, status: 'completed' | 'not_completed', reason?: string) => {
    setPendingTaskUpdates(prev => ({
      ...prev,
      [taskId]: { status, reason }
    }));
  };

  const handleAddNewTask = () => {
    // TODO: Implement add new task functionality
    Alert.alert('Add New Task', 'This feature will be implemented soon.');
  };

  const handleClockOut = async () => {
    if (!schedule) return;

    try {
      // Update all pending task statuses first
      const taskUpdatePromises = Object.entries(pendingTaskUpdates).map(([taskId, update]) =>
        updateTaskMutation.mutateAsync({
          taskId: parseInt(taskId),
          data: { status: update.status, reason: update.reason }
        })
      );

      if (taskUpdatePromises.length > 0) {
        await Promise.all(taskUpdatePromises);
      }

      // End the visit
      const mockLocation = {
        end_latitude: 40.7128,
        end_longitude: -74.0060,
        notes: 'Visit completed successfully',
      };

      await endVisitMutation.mutateAsync({
        scheduleId: schedule.id,
        data: mockLocation,
      });

      // Calculate duration for display
      const durationInSeconds = elapsedTime;
      const hours = Math.floor(durationInSeconds / 3600);
      const minutes = Math.floor((durationInSeconds % 3600) / 60);

      let durationText = '';
      if (hours > 0) {
        durationText = `${hours} hour${hours > 1 ? 's' : ''}`;
        if (minutes > 0) {
          durationText += ` ${minutes} minute${minutes > 1 ? 's' : ''}`;
        }
      } else {
        durationText = `${minutes} minute${minutes > 1 ? 's' : ''}`;
      }

      // Navigate to success screen
      navigation.navigate('ScheduleCompleted', {
        schedule,
        duration: durationText,
      });
    } catch (error) {
      Alert.alert(
        'Clock Out Failed',
        'Failed to clock out. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text variant="body" color="textSecondary">Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !schedule) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text variant="body" color="textSecondary">
            {error ? 'Error loading schedule' : 'Schedule not found'}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const currentUser = {
    name: schedule.client_name,
    avatar: undefined, // Will use default avatar
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="textPrimary" />
        </TouchableOpacity>
        <Text variant="h2" color="textPrimary" style={styles.headerTitle}>
          Clock-Out
        </Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {/* Timer */}
        <View style={styles.timerContainer}>
          <Text variant="h1" color="textPrimary" style={styles.timer}>
            {formatElapsedTime(elapsedTime)}
          </Text>
        </View>

        {/* Service Info */}
        <View style={styles.serviceSection}>
          <Text variant="h3" color="textPrimary" style={styles.serviceName}>
            {schedule.service_name || 'Service Name A'}
          </Text>
          <UserInfo
            name={schedule.client_name}
            size="large"
          />
        </View>

        {/* Tasks Section */}
        <View style={styles.tasksSection}>
          <Text variant="h3" color="textPrimary" style={styles.sectionTitle}>
            Tasks:
          </Text>
          <Text variant="body" color="textSecondary" style={styles.sectionSubtitle}>
            Please tick the tasks that you have done
          </Text>

          {schedule.tasks && schedule.tasks.length > 0 ? (
            schedule.tasks.map((task) => (
              <TaskItem
                key={task.id}
                task={{
                  ...task,
                  // Apply pending updates if any
                  status: pendingTaskUpdates[task.id]?.status || task.status,
                  reason: pendingTaskUpdates[task.id]?.reason || task.reason,
                }}
                onStatusChange={handleTaskStatusChange}
              />
            ))
          ) : (
            <Text variant="body" color="textSecondary" style={styles.noTasksText}>
              No tasks assigned for this visit.
            </Text>
          )}

          {/* Add New Task Button */}
          <TouchableOpacity style={styles.addTaskButton} onPress={handleAddNewTask}>
            <Icon name="add" size={20} color="primary" />
            <Text variant="button" color="primary" style={styles.addTaskText}>
              Add new task
            </Text>
          </TouchableOpacity>
        </View>

        {/* Clock-In Location */}
        <View style={styles.locationSection}>
          <Text variant="h3" color="textPrimary" style={styles.sectionTitle}>
            Clock-In Location
          </Text>
          <View style={styles.locationCard}>
            <View style={styles.mapPlaceholder}>
              <Icon name="location" size={40} color="primary" />
            </View>
            <View style={styles.locationInfo}>
              <Text variant="body" color="textPrimary" style={styles.locationAddress}>
                {schedule.location.address}
              </Text>
              <Text variant="body" color="textSecondary" style={styles.locationDetails}>
                {schedule.location.city}, {schedule.location.state}, {schedule.location.zip_code}
              </Text>
            </View>
          </View>
        </View>

        {/* Clock Out Button */}
        <Button
          variant="primary"
          onPress={handleClockOut}
          fullWidth
          style={styles.clockOutButton}
          disabled={endVisitMutation.isPending || updateTaskMutation.isPending}
        >
          {endVisitMutation.isPending || updateTaskMutation.isPending ? 'Processing...' : 'Clock Out'}
        </Button>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray200,
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontWeight: '600',
  },
  headerSpacer: {
    width: 40, // Same as back button to center the title
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: spacing.screenPadding,
    gap: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerContainer: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  timer: {
    fontSize: 48,
    fontWeight: 'bold',
    letterSpacing: 2,
  },
  serviceSection: {
    alignItems: 'center',
    gap: spacing.md,
  },
  serviceName: {
    fontWeight: '600',
  },
  tasksSection: {
    gap: spacing.sm,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  sectionSubtitle: {
    marginBottom: spacing.md,
  },
  noTasksText: {
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: spacing.lg,
  },
  addTaskButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    gap: spacing.sm,
    marginTop: spacing.sm,
  },
  addTaskText: {
    fontWeight: '600',
  },
  locationSection: {
    gap: spacing.sm,
  },
  locationCard: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.gray200,
    gap: spacing.md,
  },
  mapPlaceholder: {
    width: 80,
    height: 80,
    backgroundColor: colors.gray100,
    borderRadius: borderRadius.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  locationInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  locationAddress: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  locationDetails: {
    lineHeight: 20,
  },
  clockOutButton: {
    marginTop: spacing.lg,
  },
});

export default ClockOutScreen;
