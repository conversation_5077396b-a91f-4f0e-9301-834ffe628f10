# Test task update
$body = '{"status": "completed"}'
$response = Invoke-RestMethod -Uri 'http://localhost:8080/api/v1/tasks/1' -Method Put -Body $body -ContentType 'application/json'
$response | ConvertTo-Json -Depth 10

# Test task update with reason
$body2 = '{"status": "not_completed", "reason": "Client refused medication"}'
$response2 = Invoke-RestMethod -Uri 'http://localhost:8080/api/v1/tasks/2' -Method Put -Body $body2 -ContentType 'application/json'
$response2 | ConvertTo-Json -Depth 10

# Get updated schedule
$schedule = Invoke-RestMethod -Uri 'http://localhost:8080/api/v1/schedules/1' -Method Get
$schedule | ConvertTo-Json -Depth 10
