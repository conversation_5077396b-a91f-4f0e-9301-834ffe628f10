package repositories

import (
	"caregiver-shift-tracker/internal/models"
	"database/sql"
	"fmt"
	"time"
)

type taskRepository struct {
	db *sql.DB
}

// NewTaskRepository creates a new task repository
func NewTaskRepository(db *sql.DB) TaskRepository {
	return &taskRepository{db: db}
}

// GetByScheduleID retrieves all tasks for a schedule
func (r *taskRepository) GetByScheduleID(scheduleID int) ([]models.Task, error) {
	query := `
		SELECT id, schedule_id, title, description, status, reason, completed_at, created_at, updated_at
		FROM tasks 
		WHERE schedule_id = ?
		ORDER BY created_at ASC`

	rows, err := r.db.Query(query, scheduleID)
	if err != nil {
		return nil, fmt.Errorf("failed to query tasks: %w", err)
	}
	defer rows.Close()

	var tasks []models.Task
	for rows.Next() {
		var t models.Task
		var reason sql.NullString
		err := rows.Scan(&t.ID, &t.ScheduleID, &t.Title, &t.Description, &t.Status,
			&reason, &t.CompletedAt, &t.CreatedAt, &t.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan task: %w", err)
		}
		if reason.Valid {
			t.Reason = reason.String
		}
		tasks = append(tasks, t)
	}

	return tasks, nil
}

// GetByID retrieves a task by ID
func (r *taskRepository) GetByID(id int) (*models.Task, error) {
	query := `
		SELECT id, schedule_id, title, description, status, reason, completed_at, created_at, updated_at
		FROM tasks 
		WHERE id = ?`

	var t models.Task
	var reason sql.NullString
	err := r.db.QueryRow(query, id).Scan(&t.ID, &t.ScheduleID, &t.Title, &t.Description,
		&t.Status, &reason, &t.CompletedAt, &t.CreatedAt, &t.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	if reason.Valid {
		t.Reason = reason.String
	}

	return &t, nil
}

// Create creates a new task
func (r *taskRepository) Create(task *models.Task) error {
	query := `
		INSERT INTO tasks (schedule_id, title, description, status, reason, completed_at)
		VALUES (?, ?, ?, ?, ?, ?)`

	result, err := r.db.Exec(query, task.ScheduleID, task.Title, task.Description,
		task.Status, task.Reason, task.CompletedAt)
	if err != nil {
		return fmt.Errorf("failed to create task: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert id: %w", err)
	}

	task.ID = int(id)
	return nil
}

// Update updates an existing task
func (r *taskRepository) Update(task *models.Task) error {
	query := `
		UPDATE tasks 
		SET title = ?, description = ?, status = ?, reason = ?, completed_at = ?, updated_at = CURRENT_TIMESTAMP
		WHERE id = ?`

	_, err := r.db.Exec(query, task.Title, task.Description, task.Status,
		task.Reason, task.CompletedAt, task.ID)
	if err != nil {
		return fmt.Errorf("failed to update task: %w", err)
	}

	return nil
}

// UpdateStatus updates the status of a task
func (r *taskRepository) UpdateStatus(id int, status, reason string) error {
	var completedAt *time.Time
	if status == "completed" {
		now := time.Now()
		completedAt = &now
	}

	query := `
		UPDATE tasks 
		SET status = ?, reason = ?, completed_at = ?, updated_at = CURRENT_TIMESTAMP
		WHERE id = ?`

	_, err := r.db.Exec(query, status, reason, completedAt, id)
	if err != nil {
		return fmt.Errorf("failed to update task status: %w", err)
	}

	return nil
}

// Delete deletes a task
func (r *taskRepository) Delete(id int) error {
	query := "DELETE FROM tasks WHERE id = ?"
	_, err := r.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete task: %w", err)
	}
	return nil
}
